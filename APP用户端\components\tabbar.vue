<template>
	<view class="custom-tabbar fixed flex-center bg-base border-top">
		<view
			v-for="(item, index) in tabbarConfig"
			:key="item.value"
			@tap.stop="changeTab(item.value)"
			class="flex-center flex-column"
			:style="{ width: 100 / tabbarConfig.length + '%', color: isActive(item.value) ? activeColor : inactiveColor }"
		>
			<view class="icon-wrapper">
				<u-icon
					:name="item.icon"
					:color="isActive(item.value) ? activeColor : inactiveColor"
					size="28"
				></u-icon>
			</view>
			<view class="tab-text">{{ item.name }}</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapMutations
	} from "vuex";

	export default {
		name: 'CustomTabbar',
		props: {
			cur: {
				type: [Number, String],
				default: '0'
			}
		},
		data() {
			return {
				activeColor: '#599eff',
				inactiveColor: '#666',
				tmplIds: [
					'vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',
					'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',
					'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
				],
				tabbarConfig: [
					{
						name: '首页',
						icon: 'home-fill',
						value: 0,
						path: '/pages/service'
					},
					{
						name: '分类',
						icon: 'grid-fill',
						value: 1,
						path: '/pages/technician'
					},
					{
						name: '快速下单',
						icon: 'clock-fill',
						value: 3,
						path: '/pages/fast'
					},
					{
						name: '购物车',
						icon: 'shopping-cart-fill',
						value: 2,
						path: '/pages/order'
					},
			
					{
						name: '我的',
						icon: 'account-fill',
						value: 4,
						path: '/pages/mine'
					}
				]
			};
		},

		computed: {
			...mapState({
				primaryColor: (state) => state.config.configInfo.primaryColor,
				subColor: (state) => state.config.configInfo.subColor,
				configInfo: (state) => state.config.configInfo,
				commonOptions: (state) => state.user.commonOptions,
				activeIndex: (state) => state.order.activeIndex,
			}),
		},
		mounted() {
			this.updateTabbarHeight();
			console.log('Current tab:', this.cur);
		},
		methods: {
			...mapMutations(["updateConfigItem"]),
			// 检查是否为激活状态的tab
			isActive(value) {
				return String(value) === String(this.cur);
			},
			// 切换tab
			changeTab(value) {
				if (this.isActive(value)) return;
				
				const targetTab = this.tabbarConfig.find(item => String(item.value) === String(value));
				if (targetTab) {
					console.log('Navigating to:', targetTab.path);
					
					// 处理订阅消息
					this.handleSubscription();
					
					// 跳转页面
					uni.reLaunch({
						url: targetTab.path,
						fail: (err) => {
							console.error("Navigation failed:", err);
						}
					});
				}
			},
			// 处理订阅消息
			handleSubscription() {
				const userId = uni.getStorageSync('userId');
				const hasCanceledSubscription = uni.getStorageSync('hasCanceledSubscription');
				const hasShownModal = uni.getStorageSync('hasShownSubscriptionModal');
				
				if (userId && !hasCanceledSubscription) {
					console.log('Requesting subscription for user:', userId);
					// #ifdef MP-WEIXIN
					uni.requestSubscribeMessage({
						tmplIds: this.tmplIds,
						success: (res) => {
							console.log('requestSubscribeMessage success:', res, 'with tmplIds:', this.tmplIds);
							// Check if any of the template IDs were rejected
							const hasRejection = this.tmplIds.some(tmplId => res[tmplId] === 'reject');
							if (hasRejection && !hasShownModal) {
								uni.showModal({
									title: '提示',
									content: "您已关闭消息订阅，建议点击'通知管理'开启，方便及时接收师傅的服务通知。",
									cancelText: '取消',
									confirmText: '去开启',
									confirmColor: '#007AFF',
									success: (modalRes) => {
										// Set flag to prevent showing modal again
										uni.setStorageSync('hasShownSubscriptionModal', true);
										if (modalRes.confirm) {
											uni.openSetting({
												withSubscriptions: true
											});
										} else if (modalRes.cancel) {
											// Set flag in storage when user cancels
											uni.setStorageSync('hasCanceledSubscription', true);
										}
									}
								});
							}
						},
						fail: (err) => {
							console.error('requestSubscribeMessage failed:', err, 'with tmplIds:', this.tmplIds);
						}
					});
					// #endif
				}
			},
			// 计算tabbar高度并更新
			updateTabbarHeight() {
				const query = uni.createSelectorQuery().in(this);
				query
					.select('.custom-tabbar')
					.boundingClientRect(data => {
						if (data) {
							const systemInfo = uni.getSystemInfoSync();
							const tabbarHeight = data.height;
							const windowHeight = systemInfo.windowHeight;
							
							// 更新配置信息
							let configInfo = JSON.parse(JSON.stringify(this.configInfo));
							configInfo.curSysHeight = windowHeight - tabbarHeight - (configInfo.navBarHeight || 0);
							configInfo.tabbarHeight = tabbarHeight;
							this.updateConfigItem({
								key: "configInfo",
								val: configInfo,
							});
							
							console.log("Tabbar height updated:", tabbarHeight, "curSysHeight:", configInfo.curSysHeight);
						}
					})
					.exec();
			}
		}
	};
</script>

<style scoped lang="scss">
	.custom-tabbar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		height: 120rpx;
		padding-bottom: calc(env(safe-area-inset-bottom) / 2);
		background-color: #fff;
		z-index: 1000;
		border-top: 1px solid #eee;
	}
	.tab-text {
		font-size: 22rpx;
		margin-top: 5rpx;
		line-height: 32rpx;
	}
	.flex-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.flex-column {
		flex-direction: column;
	}
	.bg-base {
		background-color: #fff;
	}
	.icon-wrapper {
		position: relative;
	}
	.badge {
		position: absolute;
		top: -5rpx;
		right: -8rpx;
		width: 32rpx;
		height: 32rpx;
		background-color: #E41F19;
		color: #fff;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 20rpx;
	}
</style>
