<template>

	<view class="page">

		<tabbar :cur="0"></tabbar>
		<view class="img">
			<u-swiper :list="list1" height="108"></u-swiper>
		</view>
		<view>
		  <u-notice-bar :text="text1" direction='column'  speed="1000" ></u-notice-bar>
		</view>
		<view class="location-bar">
			<view class="location-info">
				<view class="location-text">当前接单位置：{{ province + city + district || '定位中...' }}</view>
			</view>
		</view>
		<view class="tabs-container">
			<view class="custom-tabs">
				<view  class="tab-item"   :class="{ 'active': currentTab === index }" v-for="(tab, index) in tabsList"  
					:key="index"   @click="switchTab(index)">
					<text class="tab-text">{{ tab.name }}</text>
				</view>
			</view>
		</view>

		<!-- Sub-menu for 高价值 -->
		<view class="sub-menu" v-if="showSubMenu">
			<view class="sub-tab-item" :class="{ 'active': currentSubTab === index }"
				v-for="(subTab, index) in subMenuList" :key="index" @click="switchSubTab(index)">
				<text class="sub-tab-text">{{ subTab.name }}</text>
				<text class="badge" v-if="subTab.badge">{{ subTab.badge }}</text>
			</view>
		</view>

		<view class="filter-container" v-if="showFilterContainer">
			<view class="filter-bar">
				<view class="filter-item-container">
					<view class="filter-item" @click.stop="toggleFilter('price')"
						:class="{ 'active-filter-item': isPriceFilterActive }">
						<text>{{ priceFilterText }}</text>
						<text class="arrow" :class="{ 'rotate': showFilter === 'price' }">▼</text>
					</view>
				</view>

				<view class="filter-item-container">
					<view class="filter-item" @click.stop="toggleFilter('distance')"
						:class="{ 'active-filter-item': isDistanceFilterActive }">
						<text>订单距离 {{ distance }}公里</text>
						<text class="arrow" :class="{ 'rotate': showFilter === 'distance' }">▼</text>
					</view>
				</view>

				<view class="filter-item-container">
					<view class="filter-item" @click.stop="toggleFilter('category')"
						:class="{ 'active-filter-item': isCategoryFilterActive }">
						<text>{{ currentCateName || '分类筛选' }}</text>
						<text class="arrow" :class="{ 'rotate': showFilter === 'category' }">▼</text>
					</view>
				</view>
			</view>

			<view class="filter-dropdown" v-if="showFilter === 'price'" @click.stop>
				<view class="dropdown-content">
					<view class="filter-section">
						<view class="section-title">价格范围</view>
						<view class="option-list">
							<view  class="option-item"  
								:class="{ active: priceRange.min === 0 && priceRange.max === 100 && priceRange.type === 'predefined' }"
								@click="selectPriceRange(0, 100, 'predefined')">0 - 100</view>
							<view  class="option-item"  
								:class="{ active: priceRange.min === 100 && priceRange.max === 200 && priceRange.type === 'predefined' }"
								@click="selectPriceRange(100, 200, 'predefined')">100 - 200</view>
							<view  class="option-item"  
								:class="{ active: priceRange.min === 200 && priceRange.max === 500 && priceRange.type === 'predefined' }"
								@click="selectPriceRange(200, 500, 'predefined')">200 - 500</view>
							<view  class="option-item"  
								:class="{ active: priceRange.min === 500 && priceRange.max === null && priceRange.type === 'predefined' }"
								@click="selectPriceRange(500, null, 'predefined')">500以上</view>
						</view>
					</view>

					<view class="filter-section">
						<view class="section-title">自定义价格</view>
						<view class="custom-price-inputs">
							<input  type="digit"   v-model="priceRange.customMin"   placeholder="最低价"  
								@blur="validatePriceInput('min')" />
							<text>-</text>
							<input  type="digit"   v-model="priceRange.customMax"   placeholder="最高价"  
								@blur="validatePriceInput('max')" />
						</view>
					</view>

					<view class="filter-actions">
						<view class="filter-btn reset" @click="resetPriceFilter">重置</view>
						<view class="filter-btn confirm" @click="applyPriceFilter">确定</view>
					</view>
				</view>
			</view>

			<view class="filter-dropdown" v-if="showFilter === 'distance'" @click.stop>
				<view class="dropdown-content">
					<view class="filter-section">
						<view class="section-title">距离范围</view>
						<view class="distance-input">
							<text>请输入距离范围（公里）</text>
							<view class="distance-input-container">
								<input type="digit" v-model="distance" placeholder="请输入距离"
									@blur="validateDistanceInput" />
								<text class="unit">公里</text>
							</view>
							<view class="distance-hint">范围：1-100公里</view>
						</view>
					</view>

					<view class="filter-actions">
						<view class="filter-btn reset" @click="resetDistanceFilter">重置</view>
						<view class="filter-btn confirm" @click="applyDistanceFilter">确定</view>
					</view>
				</view>
			</view>

			<view class="filter-dropdown" v-if="showFilter === 'category'" @click.stop>
				<view class="dropdown-content">
					<view class="filter-section">
						<view class="section-title">服务品类</view>
						<view class="option-list">
							<view class="option-item" :class="{ active: currentCateId === '' }"
								@click="selectClick({ id: '', name: '全部' })">全部</view>
							<view  class="option-item"   v-for="(cate, index) in cateList"   :key="index"  
								:class="{ active: currentCateId === cate.id }" @click="selectClick(cate)">
								{{ cate.name }}
							</view>
						</view>
					</view>

					<view class="filter-actions">
						<view class="filter-btn reset" @click="resetCategoryFilter">重置</view>
						<view class="filter-btn confirm" @click="applyCategoryFilter">确定</view>
					</view>
				</view>
			</view>
		</view>
<!-- 		<view class="quotation-counts">
			<view class="counts-header">
				<text class="counts-title">接单统计</text>
			</view>
			<view class="counts-row">
				<view @click="textclick" class="count-item">
					<text class="count-label">比价订单</text>
					<text class="count-value">{{ QuotationCounts.comparisonOrder ||
						0 }}/{{ QuotationCounts.nowComparisonOrder || 0 }}</text>
				</view>
				<view class="count-divider"></view>
				<view class="count-item">
					<text class="count-label">一口价订单</text>
					<text class="count-value">{{ QuotationCounts.fixedPrice || 0 }}/{{ QuotationCounts.nowFixedPrice ||
						0 }}</text>
				</view>
			</view>
		</view> -->
		<view class="check_box" v-if="false">
		</view>
		<u-empty mode="order" icon="http://cdn.uviewuni.com/uview/empty/order.png" v-if="list.length == 0"></u-empty>
		<view class="re_item" v-for="(item, index) in list" :key="index" @click="seeDetail(item)">
			<view class="top">
				<image :src="item.goodsCover" style="width: 160rpx;height: 160rpx;border-radius: 10rpx;"></image>
				<view class="order">
					<div class="title">{{ item.goodsName }}<span v-if="item.type != 0"
							style="font-size: 24rpx;color:#999;margin-left: 10rpx;">(报价0.00元起)</span></div>
					<div class="price">{{ item.type == 0 ? (item.payPrice ? `￥${item.payPrice}` : '￥***') : '待报价' }}
					</div>
				</view>
			</view>
			<view class="info" @click="dingyue()">
				<view class="address">
					<u-icon name="map-fill" color="#2979ff" size="22"></u-icon>
					<view class="right">
						<view class="address_name">{{ item.address }}</view>
						<view class="address_Info">{{ item.addressInfo }}</view>
					</view>
				</view>
				<view class="tel">
					<u-icon name="phone-fill" color="#2979ff" size="22"></u-icon>
					<view class="right">{{ item.mobile.slice(0, 3) + '********' }}</view>
				</view>
			</view>
			<view class="notes" v-if="item.text">
				<text style="color:#999999;">备注:</text>
				{{ item.text }}
			</view>
			<view class="order-stats">
				<view class="stats-row">
					<view class="stat-item">
						<text class="stat-label">数量:</text>
						<text class="stat-value">{{ item.num || 0 }}</text>
					</view>
					<view class="stat-item">
						<text class="stat-label">当前报价人数:</text>
						<text class="stat-value">{{ item.quotationNum || 0 }}</text>
					</view>
				</view>
				<view class="stats-row">
					<view class="stat-item">
						<text class="stat-label">剩余报价人数:</text>
						<text class="stat-value">{{ item.remainingNumber || 0 }}</text>
					</view>
					<view class="stat-item" v-if="item.quotationSortboolean !== undefined">
						<text class="stat-label">报价状态:</text>
						<text class="stat-value" :style="{ color: item.quotationSortboolean ? '#2979ff' : '#999' }">
							{{ item.quotationSortboolean ? '已报价' : '未报价' }}
						</text>
					</view>
				</view>
			</view>
			<view class="btn" :style="item.type == 1 ? '' : 'background-color:#2E80FE;color:#fff;'"
				@click.stop="seeDetail(item)">
				{{ item.type == 1 ? '立即报价' : '立即接单' }}
			</view>
		</view>
		<u-modal :show="confirmshow" :content="content" showCancelButton @confirm="confirmRe"
			@cancel="confirmshow = false"></u-modal>

		<u-modal :show="masterModalShow" content="您还不是师傅,请去入驻" showCancelButton @confirm="goToSettle"
			@cancel="masterModalShow = false"></u-modal>

		<u-modal :show="detailModalShow" title="服务承诺" showCancelButton cancelText="不同意" confirmText="同意"
			@confirm="confirmDetail" @cancel="detailModalShow = false"
			v-if="shifustutus.data !== -2 && shifustutus.data !== -1">
			<view class="modal-content">
				<rich-text :nodes="getconfigs ? getconfigs : configInfo.shifuQualityCommitment"></rich-text>
			</view>
		</u-modal>

		<view class="loadmore" v-if="list.length >= 10">
			<u-loadmore :status="status" />
		</view>
	</view>
</template>

<script>
import tabbar from "@/components/tabbarsf.vue";
import {
	mapState,
	mapActions
} from 'vuex';
import locationManager from '@/utils/location-manager.js';
export default {
	components: {
		tabbar
	},
	data() {
		return {
			orderData: '',
			showDingyue: false,
			showCate: false, // This seems redundant with the new filter dropdown, consider removal
			infodata: '',
			text1:'',
			tmplIds: [
				' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',
				'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',
				'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'
			],
			status: 'loadmore',
			id: '',
			shifuId: '',
			// 新增的选项卡数据
			currentTab: 0,
			tabsList: [
				{ name: '高价值', badge: '' }
			],
			// Sub-menu for 高价值
			showSubMenu: false,
			currentSubTab: 0,
			subMenuList: [
				{ name: '一口价', badge: '5' },
				{ name: '报价订单', badge: '11' }
			],
			// Filter container visibility
			showFilterContainer: false,
			listType: [{ name: '一口价' }, { name: '报价订单' }, { name: '高价值 ' }],
			list: [],
			confirmshow: false,
			masterModalShow: false,
			detailModalShow: false,
			content: '确认接下该订单吗',
			input: '',
			area_id: '',
			limit: 10, // Changed to 10 for more realistic pagination in examples
			page: 1,
			bannerList: [],
			configInfo: '',
			getconfigs: '',
			list1: [],
			lng: '',
			shifustutus: {
				data: 0,
				msg: ''
			},
			QuotationCounts: '',
			lat: '',
			cateList: [],
			currentCateId: '',
			currentCateName: '分类筛选', // Changed default to '分类筛选' for consistency
			copyCateList: [],
			province: '',
			city: '',
			district: '',
			isPageLoaded: false,
			selectedItem: null,
			// 新增的数据
			priceRange: {
				type: 'all', // 'all', 'predefined', 'custom'
				min: null,
				max: null,
				customMin: '',
				customMax: ''
			},
			distance: 20, // 默认20公里
			appliedDistance: 20, // To track applied distance for filter bar text
			// 折叠面板状态控制 - These seem unused with the new filter dropdown, consider removal
			isPriceCollapsed: true,
			isDistanceCollapsed: true,
			isCategoryCollapsed: true,
			// 筛选相关
			showFilter: null,
			currentActivityType: null,

			areaCount: 0
		};
	},
	computed: {
		...mapState({
			configInfos: (state) => state.config.configInfo,
			regeocode: (state) => state.service.regeocode,
			refreshReceiving: (state) => state.service.refreshReceiving || '',
		}),
		priceFilterText() {
			if (this.priceRange.type === 'all' || (this.priceRange.customMin === '' && this.priceRange.customMax === '')) {
				return '价格区间';
			} else if (this.priceRange.type === 'predefined') {
				return `${this.priceRange.min} - ${this.priceRange.max === null ? '500以上' : this.priceRange.max}`;
			} else if (this.priceRange.type === 'custom') {
				let min = this.priceRange.customMin || '最低价';
				let max = this.priceRange.customMax || '最高价';
				if (this.priceRange.customMin && this.priceRange.customMax) {
					return `${min} - ${max}`;
				} else if (this.priceRange.customMin) {
					return `${min}以上`;
				} else if (this.priceRange.customMax) {
					return `${max}以下`;
				}
				return '自定义价格'; // Fallback
			}
			return '价格区间';
		},
		isPriceFilterActive() {
			return this.priceRange.type !== 'all';
		},
		isDistanceFilterActive() {
			return this.distance !== 20; // Assuming 20km is the default/reset state
		},
		isCategoryFilterActive() {
			return this.currentCateId !== ''; // Assuming empty string is the default/reset state for '全部'
		}
	},
	methods: {
		// 检测当前平台
		getCurrentPlatform() {
			// #ifdef APP-PLUS
			return 'app-plus';
			// #endif
			// #ifdef MP-WEIXIN
			return 'mp-weixin';
			// #endif
			// #ifdef H5
			return 'h5';
			// #endif
			return 'unknown';
		},
		textclick() {
			this.$api.shifu.gettext()
		},
		// 新增的选项卡切换方法
		switchTab(index) {
			this.currentTab = index;
			// Show sub-menu only for 高价值 tab (index 0 since we only have 高价值 now)
			if (index === 0) {
				this.showSubMenu = true;
				this.currentSubTab = 0; // Default to first sub-tab
			} else {
				this.showSubMenu = false;
			}
			// 根据选项卡切换不同的数据
			this.page = 1;
			this.list = []; // Clear list on tab switch
			this.getListByTab(index);
			this.closeFilter(); // Close any open filter dropdown
		},

		// Sub-tab switching method
		switchSubTab(index) {
			this.currentSubTab = index;
			this.page = 1;
			this.list = [];
			// Load data based on sub-tab selection
			this.getListBySubTab(index);
			this.closeFilter();
		},

		// Toggle filter container visibility
		toggleFilterContainer() {
			this.showFilterContainer = !this.showFilterContainer;
		},

		// Helper to get order type based on current sub-tab (for 高价值)
		getOrderType(subTabIndex) {
			switch (subTabIndex) {
				case 0: return 0; // 一口价
				case 1: return 1; // 报价订单
				default: return undefined;
			}
		},

		// Helper to get menu type - always 2 for 高价值
		getMenuType() {
			return 2; // 高价值订单
		},

		// Get data based on sub-tab selection
		async getListBySubTab(subTabIndex) {
			uni.showLoading({
				title: '加载中'
			});

			try {
				const orderType = this.getOrderType(subTabIndex);
				const res = await this.$api.shifu.indexQuote({
					lng: this.lng,
					lat: this.lat,
					parentId: 0,
					pageNum: this.page,
					pageSize: this.limit,
					distance: this.appliedDistance,
					menu: this.getMenuType(),
					userId: this.infodata.userId,
					quotationNum: true,
					type: orderType
				});

				console.log(res);
				if (res.code === "-1") {
					uni.showToast({
						icon: 'none',
						title: res.msg
					}, 3000);
				} else {
					if (this.page === 1) {
						this.list = res.data || [];
					} else {
						this.list = this.list.concat(res.data || []);
					}

					if (res.data && res.data.length < this.limit) {
						this.status = 'nomore';
					} else {
						this.status = 'loadmore';
					}
				}
			} catch (error) {
				console.error('Error fetching sub-tab data:', error);
				uni.showToast({
					icon: 'none',
					title: '加载失败，请重试'
				});
				this.status = 'nomore';
			} finally {
				uni.hideLoading();
			}
		},

		// 根据选项卡获取不同的数据 (now only for 高价值 main tab)
		async getListByTab(tabIndex) {
			// Since we only have 高价值 tab now, load the first sub-tab by default
			if (tabIndex === 0) {
				this.getListBySubTab(0); // Load 一口价 by default
			}
		},

		reset() {
			this.currentCateName = '分类筛选';
			this.currentCateId = '';
			this.priceRange = {
				type: 'all',
				min: null,
				max: null,
				customMin: '',
				customMax: ''
			};
			this.distance = 20;
			this.appliedDistance = 20;
			this.page = 1;
			this.getListByTab(this.currentTab);
		},

		// This closeCate method seems redundant if using the new filter dropdown system
		closeCate() {
			this.showCate = false;
			setTimeout(() => {
				this.cateList = this.copyCateList;
			}, 500);
		},
		// This chooseCate method seems redundant if using the new filter dropdown system
		chooseCate() {
			this.showCate = !this.showCate;
		},

		async selectClick(cate) {
			// Only update the selected category without immediately applying the filter
			// The filter will be applied when '确定' is clicked in the category dropdown
			this.currentCateName = cate.name;
			this.currentCateId = cate.id;
		},
		async getCate() {
			try {
				const ress = await this.$api.shifu.serviceCate();
				let res = ress.data
				console.log(res)
				this.cateList = res || [];
				this.copyCateList = res || []; // Keep a copy if you need to reset the list later
			} catch (error) {
				uni.showToast({
					icon: 'none',
					title: '获取分类失败'
				});
			}
		},
		async seeDetail(item) {
			// 先检查用户是否已登录
			const userId = uni.getStorageSync('userId');
			const token = uni.getStorageSync('token');

			if (!userId || !token) {
				// 检测当前平台
				const platform = this.getCurrentPlatform();

				// 根据平台决定跳转页面
				let targetUrl = '/pages/login'; // APP默认跳转登录页
				let confirmText = '去登录';

				if (platform === 'mp-weixin') {
					// 小程序跳转到个人中心页面
					targetUrl = '/pages/mine';
					confirmText = '去登录';
				}

				// 用户未登录，显示提示并跳转
				uni.showModal({
					title: '提示',
					content: '请先登录后再进行操作',
					confirmText: confirmText,
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.navigateTo({
								url: targetUrl
							});
						}
					}
				});
				return;
			}

			// Fetch shifu status before opening any modal
			try {
				const res = await this.$api.shifu.getshifstutas({
					userId: userId
				});
				this.shifustutus = res;
				console.log(res);
				this.selectedItem = item;

				// 判断师傅状态
				if (this.shifustutus.data === -1) {
					// 不是师傅，显示入驻提示
					this.masterModalShow = true;
					return;
				} else if (this.shifustutus.data === 1 || this.shifustutus.data === 4) {
					// 其他状态显示相应提示
					uni.showToast({
						icon: 'none',
						title: this.shifustutus.msg || '无法进行此操作'
					});
					return;
				} else {
					// 正常状态，显示服务承诺弹窗
					this.detailModalShow = true;
				}
			} catch (error) {
				console.error("Error checking shifu status:", error);
				uni.showToast({
					icon: 'none',
					title: '检查身份失败'
				});
			}
		},
		confirmDetail() {
			if (this.selectedItem) {
				console.log(this.selectedItem)
				uni.setStorageSync('selectedOrder', this.selectedItem);
				uni.navigateTo({
					url: `/shifu/master_order_details?id=${this.selectedItem.id}&goodsId=${this.selectedItem.goodsId}&type=${this.selectedItem.type}`
				});
			}
			this.detailModalShow = false;
			this.selectedItem = null;
		},
		async getList() {
			uni.showLoading({
				title: '加载中'
			});
			try {
				// 使用统一的定位管理器，避免重复调用
				const locationData = await locationManager.getLocation({ forceUpdate: false, silent: true });

				if (locationData) {
					// 设置页面显示的地理位置信息
					this.lng = locationData.lng || '';
					this.lat = locationData.lat || '';
					this.province = locationData.province || '';
					this.city = locationData.city || '';
					this.district = locationData.district || '';

					// 更新 Vuex store
					if (locationData.regeocode) {
						this.$store.dispatch('setRegeocode', {
							regeocode: locationData.regeocode,
							lat: this.lat,
							lng: this.lng
						});
					}

					console.log("定位获取成功:", locationData);
				} else {
					// 如果定位失败，使用默认值
					this.province = '安徽省';
					this.city = '合肥市';
					this.district = '蜀山区';
					console.log("使用默认定位信息");
				}
			} catch (locationError) {
				console.error("定位获取失败:", locationError);
				this.province = '安徽省'; // Default province
				this.city = '阜阳市'; // Default city
				this.district = '临泉县'; // Default district

				// 保存默认地理位置数据
				const defaultLocationData = {
					province: this.province,
					city: this.city,
					county: this.district,
					address: `${this.province}${this.city}${this.district}`,
					lng: this.lng || '',
					lat: this.lat || ''
				};
				console.log(defaultLocationData);
				uni.setStorageSync('locationData', defaultLocationData);
			}

			try {
				// Initial list fetch will correspond to the default tab (聚合订单, tabIndex 0)
				const res = await this.$api.shifu.indexQuote({
					lng: this.lng,
					lat: this.lat,
					parentId: 0,
					pageNum: this.page,
					pageSize: this.limit,
					distance: this.appliedDistance, // Ensure initial load uses appliedDistance
					menu: this.getMenuType(this.currentTab), // Include menu for initial load
					userId: this.infodata.userId, // Assuming userId is available in infodata
					quotationNum: true // As per requirement
					// No type parameter for initial聚合订单 load
				});
				console.log(res);
				if (res.code === "-1") {
					uni.showToast({
						icon: 'none',
						title: res.msg
					}, 3000);
				}
				console.log(res);
				this.$set(this, 'list', res.data.list || []);
				let count = this.list.length;
				uni.setStorageSync('listCount', count);
				this.status = (res.data.list && res.data.list.length < this.limit) ? 'nomore' : 'loadmore';
				this.$forceUpdate();
			} catch (error) {
				console.error("Error fetching initial list:", error);
				this.$set(this, 'list', []);
				this.status = 'nomore';
			} finally {
				uni.hideLoading();
			}
		},
		handleReceive(item) {
			// this.textsss(); // Assuming this is defined elsewhere or not critical
			this.orderData = item;
			this.id = item.id;
			if (item.type == 0) { // Assuming type 0 means fixed price that can be directly received
				this.confirmshow = true;
			}
		},
		confirmRe() {
			this.confirmshow = false;
			this.$api.shifu.rece_Order({
				order_id: this.id
			}).then(res => {
				this.getList();
				uni.showToast({
					icon: 'success',
					title: '接单成功',
					duration: 1000
				});
				setTimeout(() => {
					uni.navigateTo({
						url: '/shifu/master_my_order'
					});
				}, 1000);
			}).catch(error => {
				uni.showToast({
					icon: 'fail',
					title: error.message || '接单失败'
				});
			});
		},
		goToSettle() {
			this.masterModalShow = false;
			uni.navigateTo({
				url: '/shifu/Settle'
			});
		},
		async getServiceInfo() {
			try {
				const res = await this.$api.shifu.index({
					city_id: this.area_id
				});
				console.log(res)
				this.bannerList = res.data || [];
				this.list1 = res.data.map(item => item.img) || [];
				if (!this.list1.length) {
					this.list1 = [
						'https://zskj.asia/attachment/image/666/24/09/2bdd13fab41b42b987bcfc501aa535bb.jpg'
					];
				}
			} catch (error) {
				// uni.showToast({
				// 	icon: 'none',
				// 	title: '获取轮播图失败'
				// });
				this.list1 = ['https://zskj.asia/attachment/image/666/24/09/e790eea3f21b4f48ab2b00b034468035.jpg'];
			}
		},
		onReachBottom() {
			if (this.status == 'nomore') return;
			this.status = 'loading';
			this.page++;
			let apiParams = {
				pageNum: this.page,
				pageSize: this.limit,
				parentId: this.currentCateId || 0,
				lat: this.lat,
				lng: this.lng,
				distance: this.appliedDistance, // Include appliedDistance for pagination
				minPrice: this.priceRange.type !== 'all' ? this.priceRange.min : undefined,
				maxPrice: this.priceRange.type !== 'all' ? this.priceRange.max : undefined,
				userId: this.infodata.userId, // Assuming userId is available in infodata
				quotationNum: true // As per requirement
			};

			// Apply the 'type' and 'menu' parameter based on the current tab for pagination
			apiParams.type = this.getOrderType(this.currentTab);
			apiParams.menu = this.getMenuType(this.currentTab);

			// Clean up undefined values from apiParams
			Object.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);

			this.$api.shifu.indexQuote(apiParams).then(res => {
				if (!res.data || !res.data.list || res.data.list.length === 0) {
					console.log(res)
					this.status = 'nomore';
					uni.showToast({
						icon: 'none',
						title: '没有更多数据了'
					});
					return;
				}
				this.$set(this, 'list', [...this.list, ...(res.data.list || [])]);
				if (res.data.list.length < this.limit) {
					this.status = 'nomore';
				} else {
					this.status = 'loadmore';
				}
			}).catch(error => {
				this.status = 'nomore';
				uni.showToast({
					icon: 'none',
					title: '加载失败，请稍后重试'
				});
			});
		},
		async initializePage() {
			uni.showLoading({
				title: '初始化中'
			});
			try {
				// const systemInfo = uni.getSystemInfoSync(); // This line is not used
				await this.getServiceInfo();
				await this.getCate();
				await this.getList(); // This will load the '聚合订单' initially

				// Get userId from storage if available
				if (uni.getStorageSync('shiInfo')) {
					this.infodata = JSON.parse(uni.getStorageSync('shiInfo'));
				}
			
		
		
				this.configInfo = uni.getStorageSync('configInfo');
				this.$forceUpdate();
			} catch (error) {
				console.error("Error initializing page:", error);
				uni.showToast({
					icon: 'none',
					title: '初始化失败，请稍后重试'
				});
			} finally {
				uni.hideLoading();
			}
		},
		// 新增的方法
		selectPriceRange(min, max, type) {
			this.priceRange = {
				type: type,
				min: min,
				max: max,
				customMin: min !== null ? min.toString() : '',
				customMax: max !== null ? max.toString() : ''
			};
		},

		validatePriceInput(type) {
			if (type === 'min') {
				const min = parseFloat(this.priceRange.customMin);
				if (!isNaN(min) && min >= 0) {
					this.priceRange.min = min;
				} else {
					this.priceRange.customMin = '';
					this.priceRange.min = null;
				}
			} else if (type === 'max') {
				const max = parseFloat(this.priceRange.customMax);
				if (!isNaN(max) && max > 0) {
					this.priceRange.max = max;
				} else {
					this.priceRange.customMax = '';
					this.priceRange.max = null;
				}
			}

			// Determine priceRange.type based on custom inputs or predefined selection
			const hasCustomMin = this.priceRange.customMin !== '';
			const hasCustomMax = this.priceRange.customMax !== '';

			if (hasCustomMin || hasCustomMax) {
				this.priceRange.type = 'custom';

				// Check if custom inputs match any predefined range for highlighting
				const predefinedRanges = [
					{ min: 0, max: 100 },
					{ min: 100, max: 200 },
					{ min: 200, max: 500 },
					{ min: 500, max: null }
				];
				let matchedPredefined = false;
				for (const range of predefinedRanges) {
					if (this.priceRange.min === range.min && this.priceRange.max === range.max) {
						this.priceRange.type = 'predefined';
						matchedPredefined = true;
						break;
					}
				}
				if (!matchedPredefined && (hasCustomMin || hasCustomMax)) {
					this.priceRange.type = 'custom';
				}
			} else if (!this.priceRange.min && !this.priceRange.max) {
				this.priceRange.type = 'all';
			} else if (this.priceRange.type !== 'predefined') {
				// Fallback if somehow min/max are set but custom inputs are empty and not predefined
				this.priceRange.type = 'custom';
			}
		},

		validateDistanceInput() {
			const distance = parseFloat(this.distance);
			if (isNaN(distance) || distance < 1) {
				this.distance = 1;
				uni.showToast({
					icon: 'none',
					title: '距离不能小于1公里'
				});
			} else if (distance > 100) {
				this.distance = 100;
				uni.showToast({
					icon: 'none',
					title: '距离不能大于100公里'
				});
			} else {
				this.distance = Math.round(distance);
			}
		},

		// This applyFilters method is a general one; the specific apply methods (price, distance, category) will call fetchFilteredData
		applyFilters() {
			// This method can be used if there was a single "Apply All Filters" button.
			// Since we have separate "确定" buttons for each dropdown, we will use
			// applyPriceFilter, applyDistanceFilter, applyCategoryFilter.
			// For now, it will just call the common fetchFilteredData with current state.
			this.fetchFilteredData(this.buildFilterParams());
			this.closeFilter();
		},

		// Adding a helper to build common API parameters
		buildFilterParams() {
			let apiParams = {
				lng: this.lng,
				lat: this.lat,
				pageNum: 1, // Always reset to page 1 on new filter application
				pageSize: this.limit,
				parentId: this.currentCateId || 0,
				distance: this.appliedDistance,
				userId: this.infodata.userId, // Assuming userId is available in infodata
				quotationNum: true // As per requirement
			};

			// Add price range parameters
			if (this.priceRange.type !== 'all') {
				apiParams.minPrice = this.priceRange.min;
				apiParams.maxPrice = this.priceRange.max;
			}

			// Set type and menu based on the current active tab
			apiParams.type = this.getOrderType(this.currentTab);
			apiParams.menu = this.getMenuType(this.currentTab);

			// Clean up undefined values from apiParams
			Object.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);

			return apiParams;
		},

		// Adding collapse functionality (if still needed, though current UI uses show/hide)
		toggleCollapse(type) {
			if (type === 'price') {
				this.isPriceCollapsed = !this.isPriceCollapsed;
			} else if (type === 'distance') {
				this.isDistanceCollapsed = !this.isDistanceCollapsed;
			} else if (type === 'category') {
				this.isCategoryCollapsed = !this.isCategoryCollapsed;
			}
		},
		// 筛选相关方法
		toggleFilter(filter) {
			if (this.showFilter === filter) {
				this.showFilter = null;
			} else {
				this.showFilter = filter;
			}
		},

		closeFilter() {
			this.showFilter = null;
		},
		// These activity, area, and sort related methods seem to be remnants and aren't tied to the current UI
		// They should be removed if not implemented in the template.
		selectActivityType(item) {
			this.currentActivityType = item.value;
		},
		applyActivityFilter() {
			let apiParams = this.buildFilterParams();
			if (this.currentActivityType) {
				apiParams.activityType = this.currentActivityType;
			}
			this.fetchFilteredData(apiParams);
			this.closeFilter();
		},
		resetActivityFilter() {
			this.currentActivityType = '';
		},
		selectArea(item) {
			this.currentArea = item.name;
		},
		applyAreaFilter() {
			let apiParams = this.buildFilterParams();
			if (this.currentArea) {
				const selectedArea = this.areaList.find(item => item.name === this.currentArea);
				if (selectedArea) {
					apiParams.area = selectedArea.value;
				}
			}
			this.fetchFilteredData(apiParams);
			this.closeFilter();
		},
		resetAreaFilter() {
			this.currentArea = '';
		},
		selectSort(item) {
			this.currentSort = item.name;
		},
		applySortFilter() {
			let apiParams = this.buildFilterParams();
			if (this.currentSort) {
				const selectedSort = this.sortOptions.find(item => item.name === this.currentSort);
				if (selectedSort) {
					apiParams.sort = selectedSort.value;
				}
			}
			this.fetchFilteredData(apiParams);
			this.closeFilter();
		},
		resetSortFilter() {
			this.currentSort = '';
		},

		applyAdvancedFilter() {
			// This method is redundant if filters are applied individually.
			// If it's for a "master" apply button, it would call fetchFilteredData with current state.
			this.fetchFilteredData(this.buildFilterParams());
			this.closeFilter();
		},

		resetAdvancedFilter() {
			this.priceRange = {
				type: 'all',
				min: null,
				max: null,
				customMin: '',
				customMax: ''
			};
			this.distance = 20;
			this.appliedDistance = 20;
			this.currentCateId = '';
			this.currentCateName = '分类筛选';
			this.page = 1; // Reset page on full reset
			this.fetchFilteredData(this.buildFilterParams()); // Apply reset
			this.closeFilter();
		},

		// 统一的数据获取方法
		async fetchFilteredData(apiParams) {
			uni.showLoading({
				title: '加载中'
			});
			this.list = []; // Clear list before fetching new data
			this.page = 1; // Ensure page is reset when new filters are applied

			// Ensure userId is consistently added if infodata is available
			// if (this.infodata && this.infodata.userId) {
			// 	apiParams.userId = this.infodata.userId;
			// }

			try {
				const res = await this.$api.shifu.indexQuote(apiParams);
				console.log(res)
				if (res.code === "-1") {
					uni.showToast({
						icon: 'none',
						title: res.msg
					}, 3000);
				}

				this.$set(this, 'list', res.data.list || []);
				let count = this.list.length;
				uni.setStorageSync('listCount', count);
				this.status = (res.data.list && res.data.list.length < this.limit) ? 'nomore' : 'loadmore';
				this.$forceUpdate();
			} catch (error) {
				console.error("Error fetching filtered data:", error);
				this.$set(this, 'list', []);
				this.status = 'nomore';
			} finally {
				uni.hideLoading();
			}
		},
		// 价格筛选相关方法
		applyPriceFilter() {
			this.fetchFilteredData(this.buildFilterParams());
			this.closeFilter();
		},

		resetPriceFilter() {
			this.priceRange = {
				type: 'all',
				min: null,
				max: null,
				customMin: '',
				customMax: ''
			};
			this.applyPriceFilter(); // Apply reset immediately
		},

		// 距离筛选相关方法
		applyDistanceFilter() {
			this.appliedDistance = this.distance; // Update applied distance
			this.fetchFilteredData(this.buildFilterParams());
			this.closeFilter();
		},

		resetDistanceFilter() {
			this.distance = 20; // Reset slider value
			this.appliedDistance = 20; // Reset applied value
			this.applyDistanceFilter(); // Apply reset immediately
		},

		// 分类筛选相关方法
		applyCategoryFilter() {
			this.fetchFilteredData(this.buildFilterParams());
			this.closeFilter();
		},

		resetCategoryFilter() {
			this.currentCateId = '';
			this.currentCateName = '分类筛选';
			this.applyCategoryFilter(); // Apply reset immediately
		},

		// 获取接单统计数据
		// async getQuotationCounts() {
		// 	try {
		// 		const res = await this.$api.shifu.getQuotationCounts();
		// 		if (res.code === '-1') {
		// 			console.error('获取接单统计失败:', res.msg);
		// 		} else {
		// 			this.QuotationCounts = res.data;
		// 		}
		// 	} catch (error) {
		// 		console.error('获取接单统计失败:', error);
		// 	}
		// },
	},
	async onLoad(query) {
		
		const scene = decodeURIComponent(query.scene || '');
		console.log('开始获取 scene:', scene);
		if (scene) {
			this.$store.commit('setErweima', scene);
			uni.setStorageSync('erweima', scene);
			console.log('已存储 scene:', scene);
		} else {
			console.log('未获取到 scene 参数');
		}
	
			
	
		this.$api.shifu.gonggao().then(res=>{
				// Extract content from the first item in the data array
				if(res.data && res.data.length > 0) {
					this.text1 = res.data[0].content
				}
			})

		// Initialize sub-menu to show by default since we only have 高价值 tab
		this.showSubMenu = true;
			
		
		
		
		
		
		
		
		
		
		this.$api.base.getConfig().then(res => {
			this.getconfigs = res.data.shifuQualityCommitment
		})
		this.$api.shifu.getQuotationCounts().then(res => {
			if (res.code === '-1') {
				uni.showToast({
					icon: 'none',
					title: res.msg
				}, 3000);
			} else {
				this.QuotationCounts = res.data
			}

		});

		if (uni.getStorageSync('shiInfo')) {
			this.infodata = JSON.parse(uni.getStorageSync('shiInfo'));
		}

		// 注册刷新事件监听器
		uni.$on('refreshReceivingList', () => {
			console.log('收到刷新事件，开始刷新页面数据');
			this.page = 1;
			this.list = [];
			this.getListByTab(this.currentTab);
			// 同时刷新接单统计数据
			// this.getQuotationCounts();
		});

		this.isPageLoaded = true;
		await this.initializePage();
	},
	async onPullDownRefresh() {
		try {
			// 重置页面状态
			this.page = 1;
			this.list = [];
			this.status = 'loadmore';

			// 重新获取数据
			await this.getListByTab(this.currentTab);

			// 刷新接单统计
			await this.getQuotationCounts();

			uni.showToast({
				title: '刷新成功',
				icon: 'success',
				duration: 1000
			});
		} catch (error) {
			console.error('下拉刷新失败:', error);
			uni.showToast({
				title: '刷新失败，请重试',
				icon: 'none'
			});
		} finally {
			uni.stopPullDownRefresh();
		}
	},
	async onShow() {
		console.log('shifuIndex onShow 触发');

		// 延迟检查刷新标志，确保 onUnload 已经执行
		setTimeout(async () => {
			const needRefresh = uni.getStorageSync('needRefreshShifuIndex');
			const app = getApp();
			const globalNeedRefresh = app && app.globalData && app.globalData.needRefreshShifuIndex;

			console.log('检查刷新标志:', {
				storage: needRefresh,
				global: globalNeedRefresh
			});

			if (needRefresh || globalNeedRefresh) {
				console.log('检测到刷新标志，开始刷新页面数据');
				// 清除刷新标志
				uni.removeStorageSync('needRefreshShifuIndex');
				if (app && app.globalData) {
					app.globalData.needRefreshShifuIndex = false;
				}
				// 刷新页面数据
				this.page = 1;
				this.list = [];
				await this.getListByTab(this.currentTab);
				await this.getQuotationCounts();
				console.log('页面数据刷新完成');
			}
		}, 100);

		// Ensure shifu status is checked on show if not already done, or if it needs to be refreshed
		this.$api.shifu.getshifstutas({
			userId: uni.getStorageSync('userId')
		}).then(res => {
			console.log(res)
			this.shifustutus = res;
		});
	},
	onHide() {
		uni.$off('refreshReceivingList'); // Unregister event listener when page hides
	},
	onUnload() {
		uni.$off('refreshReceivingList'); // Unregister event listener when page unloads
	}
};
</script>

<style scoped lang="scss">
.page {
	min-height: 100vh;
	background-color: #f8f9fa;
	padding-bottom: 120rpx;
}

.img {
	width: 690rpx;
	margin: 20rpx auto;
	border-radius: 12rpx;
	overflow: hidden;
}

.location-bar {
	display: flex;
	align-items: center;
	padding: 16rpx 20rpx;
	background-color: #fff;
	margin: 0 20rpx;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	font-size: 26rpx;
	color: #666;
}

.location-info {
	flex: 1;
}

.location-text {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.tabs-container {
	margin: 20rpx;
	background-color: #fff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.tabs-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20rpx;
}

.custom-tabs {
	display: flex;
	flex: 1;
}

.filter-toggle {
	padding: 10rpx 15rpx;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #666;
	cursor: pointer;
	transition: background-color 0.3s;
}

.filter-toggle:active {
	background-color: #e0e0e0;
}

.tab-item {
	flex: 1;
	flex-direction: column;
	padding: 20rpx 0;
	text-align: center;
	position: relative;
	font-size: 28rpx;
	color: #666;
	transition: color 0.3s;

	&.active {
		color: #2E80FE;
		font-weight: 600;

		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%);
			width: 40rpx;
			height: 4rpx;
			background-color: #2E80FE;
			border-radius: 2rpx;
		}
	}
}

.sub-menu {
	display: flex;
	background-color: #f8f9fa;
	padding: 10rpx 20rpx;
	margin: 0 20rpx;
	border-radius: 0 0 12rpx 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.sub-tab-item {
	flex: 1;
	padding: 15rpx 0;
	text-align: center;
	position: relative;
	font-size: 26rpx;
	color: #666;
	transition: color 0.3s;
	display: flex;
	align-items: center;
	justify-content: center;

	&.active {
		color: #2E80FE;
		font-weight: 600;
	}
}

.sub-tab-text {
	margin-right: 8rpx;
}

.badge {
	background-color: #ff4757;
	color: white;
	font-size: 20rpx;
	padding: 2rpx 8rpx;
	border-radius: 10rpx;
	min-width: 16rpx;
	text-align: center;
}

.filter-container {
	background-color: #fff;
	padding: 0 20rpx;
	position: sticky;
	top: 0;
	z-index: 10;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.filter-bar {
	display: flex;
	justify-content: space-between;
	padding: 12rpx 0;
}

.filter-item-container {
	flex: 1;
	display: flex;
	justify-content: center;
}

.filter-item {
	display: flex;
	align-items: center;
	font-size: 26rpx;
	color: #333;
	padding: 8rpx 20rpx;
	border-radius: 40rpx;
	transition: background-color 0.3s, color 0.3s;

	&.active-filter-item {
		background-color: #e6f0ff;
		color: #2E80FE;
	}
}

.arrow {
	margin-left: 8rpx;
	font-size: 20rpx;
	color: #aaa;
	transition: transform 0.3s ease;
}

.arrow.rotate {
	transform: rotate(180deg);
}

.filter-dropdown {
	position: absolute;
	top: 100%;
	left: 0;
	width: 100%;
	background-color: #fff;
	border-radius: 0 0 16rpx 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	padding: 24rpx;
	z-index: 999;
	animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(-10rpx);
	}

	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.dropdown-content {
	max-height: 60vh;
	overflow-y: auto;
}

.filter-section {
	margin-bottom: 24rpx;
}

.section-title {
	font-size: 28rpx;
	font-weight: 500;
	color: #333;
	margin-bottom: 16rpx;
}

.option-list {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 16rpx;
}

.option-item {
	padding: 16rpx;
	text-align: center;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	font-size: 26rpx;
	color: #666;
	transition: background-color 0.3s, color 0.3s;

	&.active {
		background-color: #2E80FE;
		color: #fff;
	}
}

.custom-price-inputs {
	display: flex;
	align-items: center;
	background-color: #f8f9fa;
	border-radius: 12rpx;
	padding: 8rpx;

	input {
		flex: 1;
		height: 60rpx;
		padding: 0 16rpx;
		border: none;
		background: transparent;
		font-size: 26rpx;
		color: #333;
		text-align: center;
	}

	text {
		color: #999;
		font-size: 26rpx;
		padding: 0 8rpx;
	}
}

.distance-input {
	>text {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 12rpx;
		display: block;
	}

	.distance-input-container {
		display: flex;
		align-items: center;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		padding: 0 16rpx;

		input {
			flex: 1;
			height: 60rpx;
			font-size: 26rpx;
			color: #333;
			background: transparent;
			border: none;
		}

		.unit {
			font-size: 26rpx;
			color: #666;
		}
	}

	.distance-hint {
		font-size: 22rpx;
		color: #999;
		margin-top: 8rpx;
		text-align: right;
	}
}

.filter-actions {
	display: flex;
	gap: 16rpx;
	margin-top: 24rpx;
}

.filter-btn {
	flex: 1;
	height: 72rpx;
	line-height: 72rpx;
	text-align: center;
	border-radius: 36rpx;
	font-size: 28rpx;
	font-weight: 500;
	transition: opacity 0.3s;

	&.reset {
		background-color: #f8f9fa;
		color: #666;
	}

	&.confirm {
		background-color: #2E80FE;
		color: #fff;
	}
}

.quotation-counts {
	margin: 20rpx;
	padding: 20rpx;
	background-color: #fff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.counts-header {
	text-align: center;
	margin-bottom: 16rpx;

	.counts-title {
		font-size: 28rpx;
		font-weight: 500;
		color: #333;
	}
}

.counts-row {
	display: flex;
	justify-content: space-around;
	align-items: center;
}

.count-item {
	text-align: center;

	.count-label {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 4rpx;
	}

	.count-value {
		font-size: 28rpx;
		font-weight: 600;
		color: #2E80FE;
	}
}

.count-divider {
	width: 1rpx;
	height: 40rpx;
	background-color: #eee;
}

.re_item {
	margin: 20rpx;
	padding: 24rpx;
	background-color: #fff;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.top {
	display: flex;
	gap: 20rpx;

	image {
		width: 160rpx;
		height: 160rpx;
		border-radius: 12rpx;
	}

	.order {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.title {
			font-size: 28rpx;
			font-weight: 500;
			color: #333;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.price {
			font-size: 30rpx;
			font-weight: 600;
			color: #2E80FE;
		}
	}
}

.info {
	margin-top: 20rpx;
	padding-top: 20rpx;
	border-top: 1rpx solid #eee;

	.address,
	.tel {
		display: flex;
		align-items: flex-start;
		margin-bottom: 12rpx;

		.left {
			margin-right: 16rpx;
			padding-top: 4rpx;
		}

		.right {
			flex: 1;
			font-size: 26rpx;
			color: #333;

			.address_name {
				font-weight: 500;
			}

			.address_Info {
				color: #666;
				margin-top: 4rpx;
			}
		}
	}
}

.notes {
	margin-top: 16rpx;
	padding: 16rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;

	view {
		color: #999;
		margin-bottom: 8rpx;
	}
}

.order-stats {
	margin-top: 16rpx;
	padding: 16rpx;
	background-color: #f8f9fa;
	border-radius: 8rpx;

	.stats-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 12rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.stat-item {
		display: flex;
		align-items: center;
		gap: 8rpx;

		.stat-label {
			font-size: 24rpx;
			color: #666;
		}

		.stat-value {
			font-size: 26rpx;
			font-weight: 500;
			color: #333;
		}
	}
}

.btn {
	margin-top: 20rpx;
	height: 72rpx;
	line-height: 72rpx;
	text-align: center;
	border-radius: 36rpx;
	font-size: 28rpx;
	font-weight: 500;
	color: #2E80FE;
	border: 1rpx solid #2E80FE;
	transition: background 0.3s;

	&[style*="background-color"] {
		background-color: #2E80FE !important;
		color: #fff !important;
		border: none;
	}
}

.loadmore {
	padding: 20rpx 0;
	text-align: center;
}

.modal-content {
	padding: 20rpx;
	max-height: 400rpx;
	overflow-y: auto;
	font-size: 26rpx;
	line-height: 1.6;
	color: #333;
}
</style>